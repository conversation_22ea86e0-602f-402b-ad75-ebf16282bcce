// Test file to verify subtitle extraction functionality
// This tests the parseSRTContent function

// Mock SRT content (bilingual format)
const mockBilingualSRT = `1
00:00:01,500 --> 00:00:04,000
Hello, welcome to our video.
<PERSON><PERSON> chào, chào mừng bạn đến với video của chúng tôi.

2
00:00:04,500 --> 00:00:07,200
Today we will learn about AI.
Hôm nay chúng ta sẽ học về AI.

3
00:00:07,700 --> 00:00:10,300
This is very exciting!
Điều này rất thú vị!`;

// Mock SRT content (single language format)
const mockSingleSRT = `1
00:00:01,500 --> 00:00:04,000
Hello, welcome to our video.

2
00:00:04,500 --> 00:00:07,200
Today we will learn about AI.

3
00:00:07,700 --> 00:00:10,300
This is very exciting!`;

// Simple implementation of parseSRTContent for testing
function parseSRTContent(srtContent) {
  const subtitles = [];
  
  // Split by double newlines to separate subtitle blocks
  const blocks = srtContent.split(/\n\s*\n/).filter(block => block.trim());
  
  for (const block of blocks) {
    const lines = block.trim().split('\n');
    
    // Skip blocks that don't have at least 3 lines (index, timestamp, text)
    if (lines.length < 3) continue;
    
    // Parse the timestamp line (second line)
    const timeLine = lines[1].trim();
    const timeMatch = timeLine.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
    
    if (!timeMatch) continue;
    
    const [, startTime, endTime] = timeMatch;
    
    // Combine all text lines (from line 3 onwards)
    const textLines = lines.slice(2);
    
    // Check if this is a bilingual subtitle (contains both original and translated text)
    let originalText = '';
    let translatedText = '';
    
    if (textLines.length >= 2) {
      // Assume first line is original, second line is translated
      originalText = textLines[0].trim();
      translatedText = textLines[1].trim();
    } else {
      // Single line - put it in original text
      originalText = textLines[0].trim();
      translatedText = '';
    }
    
    subtitles.push({
      id: `test-${subtitles.length + 1}`,
      startTime,
      endTime,
      originalText,
      translatedText,
    });
  }
  
  return subtitles;
}

// Test bilingual SRT parsing
console.log('Testing bilingual SRT parsing:');
const bilingualResult = parseSRTContent(mockBilingualSRT);
console.log('Parsed subtitles:', bilingualResult.length);
bilingualResult.forEach((sub, index) => {
  console.log(`${index + 1}. ${sub.startTime} --> ${sub.endTime}`);
  console.log(`   Original: "${sub.originalText}"`);
  console.log(`   Translated: "${sub.translatedText}"`);
});

console.log('\n' + '='.repeat(50) + '\n');

// Test single language SRT parsing
console.log('Testing single language SRT parsing:');
const singleResult = parseSRTContent(mockSingleSRT);
console.log('Parsed subtitles:', singleResult.length);
singleResult.forEach((sub, index) => {
  console.log(`${index + 1}. ${sub.startTime} --> ${sub.endTime}`);
  console.log(`   Original: "${sub.originalText}"`);
  console.log(`   Translated: "${sub.translatedText}"`);
});

console.log('\nTest completed successfully!');
