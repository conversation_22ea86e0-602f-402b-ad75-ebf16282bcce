// Test file to verify subtitle extraction functionality
// This tests the parseSRTContent function

// Mock SRT content (bilingual format)
const mockBilingualSRT = `1
00:00:01,500 --> 00:00:04,000
Hello, welcome to our video.
<PERSON><PERSON> chào, chào mừng bạn đến với video của chúng tôi.

2
00:00:04,500 --> 00:00:07,200
Today we will learn about AI.
Hôm nay chúng ta sẽ học về AI.

3
00:00:07,700 --> 00:00:10,300
This is very exciting!
Điều này rất thú vị!`;

// Mock SRT content (single language format)
const mockSingleSRT = `1
00:00:01,500 --> 00:00:04,000
Hello, welcome to our video.

2
00:00:04,500 --> 00:00:07,200
Today we will learn about AI.

3
00:00:07,700 --> 00:00:10,300
This is very exciting!`;

// Improved implementation of parseSRTContent for testing
function parseSRTContent(srtContent) {
  const subtitles = [];

  if (!srtContent || typeof srtContent !== 'string') {
    console.warn('Invalid SRT content provided');
    return subtitles;
  }

  // Normalize line endings and split into lines
  const lines = srtContent.replace(/\r\n/g, '\n').replace(/\r/g, '\n').split('\n');

  let currentSubtitle = null;
  let lineIndex = 0;

  while (lineIndex < lines.length) {
    const line = lines[lineIndex].trim();

    // Skip empty lines
    if (!line) {
      // If we have a current subtitle with content, save it
      if (currentSubtitle && currentSubtitle.startTime && currentSubtitle.endTime && currentSubtitle.textLines.length > 0) {
        const processedSubtitle = processSubtitleEntry(currentSubtitle);
        if (processedSubtitle) {
          subtitles.push(processedSubtitle);
        }
        currentSubtitle = null;
      }
      lineIndex++;
      continue;
    }

    // Check if this line is a subtitle index (number)
    const indexMatch = line.match(/^\d+$/);
    if (indexMatch) {
      // Save previous subtitle if exists
      if (currentSubtitle && currentSubtitle.startTime && currentSubtitle.endTime && currentSubtitle.textLines.length > 0) {
        const processedSubtitle = processSubtitleEntry(currentSubtitle);
        if (processedSubtitle) {
          subtitles.push(processedSubtitle);
        }
      }

      // Start new subtitle
      currentSubtitle = {
        index: parseInt(indexMatch[0]),
        textLines: []
      };
      lineIndex++;
      continue;
    }

    // Check if this line is a timestamp
    const timeMatch = line.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
    if (timeMatch && currentSubtitle) {
      currentSubtitle.startTime = timeMatch[1];
      currentSubtitle.endTime = timeMatch[2];
      lineIndex++;
      continue;
    }

    // This must be subtitle text
    if (currentSubtitle) {
      currentSubtitle.textLines.push(line);
    }

    lineIndex++;
  }

  // Don't forget the last subtitle
  if (currentSubtitle && currentSubtitle.startTime && currentSubtitle.endTime && currentSubtitle.textLines.length > 0) {
    const processedSubtitle = processSubtitleEntry(currentSubtitle);
    if (processedSubtitle) {
      subtitles.push(processedSubtitle);
    }
  }

  console.log(`Parsed ${subtitles.length} subtitle entries from SRT content`);
  return subtitles;
}

// Helper function to process a subtitle entry
function processSubtitleEntry(entry) {
  if (!entry.startTime || !entry.endTime || entry.textLines.length === 0) {
    return null;
  }

  let originalText = '';
  let translatedText = '';

  // Strategy 1: If we have exactly 2 lines, treat as bilingual (original + translated)
  if (entry.textLines.length === 2) {
    originalText = entry.textLines[0].trim();
    translatedText = entry.textLines[1].trim();
  }
  // Strategy 2: If we have more than 2 lines, try to detect bilingual pattern
  else if (entry.textLines.length > 2) {
    const midPoint = Math.floor(entry.textLines.length / 2);
    const firstHalf = entry.textLines.slice(0, midPoint).join('\n').trim();
    const secondHalf = entry.textLines.slice(midPoint).join('\n').trim();

    // If both halves have content, treat as bilingual
    if (firstHalf && secondHalf) {
      originalText = firstHalf;
      translatedText = secondHalf;
    } else {
      // Otherwise, join all lines as original text
      originalText = entry.textLines.join('\n').trim();
      translatedText = '';
    }
  }
  // Strategy 3: Single line - put in original text
  else {
    originalText = entry.textLines[0].trim();
    translatedText = '';
  }

  return {
    id: `test-${Math.random().toString(36).substr(2, 9)}`,
    startTime: entry.startTime,
    endTime: entry.endTime,
    originalText,
    translatedText,
  };
}

// Test bilingual SRT parsing
console.log('Testing bilingual SRT parsing:');
const bilingualResult = parseSRTContent(mockBilingualSRT);
console.log('Parsed subtitles:', bilingualResult.length);
bilingualResult.forEach((sub, index) => {
  console.log(`${index + 1}. ${sub.startTime} --> ${sub.endTime}`);
  console.log(`   Original: "${sub.originalText}"`);
  console.log(`   Translated: "${sub.translatedText}"`);
});

console.log('\n' + '='.repeat(50) + '\n');

// Test single language SRT parsing
console.log('Testing single language SRT parsing:');
const singleResult = parseSRTContent(mockSingleSRT);
console.log('Parsed subtitles:', singleResult.length);
singleResult.forEach((sub, index) => {
  console.log(`${index + 1}. ${sub.startTime} --> ${sub.endTime}`);
  console.log(`   Original: "${sub.originalText}"`);
  console.log(`   Translated: "${sub.translatedText}"`);
});

console.log('\n' + '='.repeat(50) + '\n');

// Test edge cases that might cause missing lines
console.log('Testing edge cases:');

// Test case 1: SRT with inconsistent spacing
const inconsistentSpacingSRT = `1
00:00:01,500 --> 00:00:04,000
Hello, welcome to our video.
Xin chào, chào mừng bạn đến với video của chúng tôi.


2
00:00:04,500 --> 00:00:07,200
Today we will learn about AI.
Hôm nay chúng ta sẽ học về AI.



3
00:00:07,700 --> 00:00:10,300
This is very exciting!
Điều này rất thú vị!

`;

console.log('Testing inconsistent spacing:');
const inconsistentResult = parseSRTContent(inconsistentSpacingSRT);
console.log('Parsed subtitles:', inconsistentResult.length);

// Test case 2: SRT with multi-line text per language
const multiLineSRT = `1
00:00:01,500 --> 00:00:04,000
Hello, welcome to our video.
This is a longer subtitle.
Xin chào, chào mừng bạn đến với video của chúng tôi.
Đây là phụ đề dài hơn.

2
00:00:04,500 --> 00:00:07,200
Today we will learn about AI.
It's going to be fun!
Hôm nay chúng ta sẽ học về AI.
Nó sẽ rất thú vị!`;

console.log('\nTesting multi-line text:');
const multiLineResult = parseSRTContent(multiLineSRT);
console.log('Parsed subtitles:', multiLineResult.length);
multiLineResult.forEach((sub, index) => {
  console.log(`${index + 1}. ${sub.startTime} --> ${sub.endTime}`);
  console.log(`   Original: "${sub.originalText}"`);
  console.log(`   Translated: "${sub.translatedText}"`);
});

// Test case 3: SRT with Windows line endings
const windowsLineEndingsSRT = "1\r\n00:00:01,500 --> 00:00:04,000\r\nHello, welcome to our video.\r\nXin chào, chào mừng bạn đến với video của chúng tôi.\r\n\r\n2\r\n00:00:04,500 --> 00:00:07,200\r\nToday we will learn about AI.\r\nHôm nay chúng ta sẽ học về AI.\r\n";

console.log('\nTesting Windows line endings:');
const windowsResult = parseSRTContent(windowsLineEndingsSRT);
console.log('Parsed subtitles:', windowsResult.length);

console.log('\nAll tests completed successfully!');
