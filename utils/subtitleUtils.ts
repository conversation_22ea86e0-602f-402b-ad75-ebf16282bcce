
import { MergedSubtitle, SubtitleLine } from '../types';

export const formatSecondsToSRTTime = (totalSeconds: number): string => {
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);
  const milliseconds = Math.floor((totalSeconds * 1000) % 1000);

  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')},${String(milliseconds).padStart(3, '0')}`;
};

const isValidSRTTimeFormat = (timeStr: string): boolean => {
  return /^\d{2}:\d{2}:\d{2},\d{3}$/.test(timeStr);
};

export const generateSRTContent = (subtitles: SubtitleLine[]): string => {
  let srtContent = '';
  subtitles.forEach((sub, index) => {
    if (!isValidSRTTimeFormat(sub.startTime) || !isValidSRTTimeFormat(sub.endTime)) {
      console.warn(`Invalid time format for subtitle ID ${sub.id}. Skipping SRT entry.`);
      return;
    }
    srtContent += `${index + 1}\n`;
    srtContent += `${sub.startTime} --> ${sub.endTime}\n`;
    srtContent += `${sub.text}\n\n`;
  });
  return srtContent;
};

export const downloadSRTFile = (srtContent: string, filename: string = 'subtitles.srt'): void => {
  const blob = new Blob([srtContent], { type: 'text/srt;charset=utf-8;' });
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

// New functions for VTT generation
export const formatSRTTimeToVTTTime = (srtTime: string): string => {
  if (!isValidSRTTimeFormat(srtTime)) {
    // console.warn(`Invalid SRT time format for VTT conversion: ${srtTime}. Returning as is.`);
    return srtTime; // Or handle error appropriately
  }
  return srtTime.replace(',', '.');
};

export const generateVTTContent = (subtitles: MergedSubtitle[], type: 'original' | 'translated'): string => {
  let vttContent = 'WEBVTT\n\n';
  subtitles.forEach((sub, index) => {
    const text = type === 'original' ? sub.originalText : sub.translatedText;
    if (text.trim() === '') {
      return; // Skip empty subtitles
    }
    if (!isValidSRTTimeFormat(sub.startTime) || !isValidSRTTimeFormat(sub.endTime)) {
      console.warn(`Invalid time format for subtitle ID ${sub.id}. Skipping VTT cue.`);
      return;
    }
    vttContent += `${index + 1}\n`; // Optional cue identifier
    vttContent += `${formatSRTTimeToVTTTime(sub.startTime)} --> ${formatSRTTimeToVTTTime(sub.endTime)}\n`;
    vttContent += `${text}\n\n`;
  });
  return vttContent;
};

// Video processing utility functions
export const downloadVideoFile = (videoBlob: Blob, filename: string): void => {
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(videoBlob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const getDefaultSubtitleStyle = () => ({
  fontFamily: 'Arial',
  fontSize: 23,
  fontColor: '#eee711',
  backgroundColor: 'rgba(0,0,0,0.8)',
  position: 'bottom' as const,
  outline: true,
  outlineColor: 'black'
});

// Function to parse SRT content and convert to MergedSubtitle format
export const parseSRTContent = (srtContent: string): MergedSubtitle[] => {
  const subtitles: MergedSubtitle[] = [];

  if (!srtContent || typeof srtContent !== 'string') {
    console.warn('Invalid SRT content provided');
    return subtitles;
  }

  // Normalize line endings and split into lines
  const lines = srtContent.replace(/\r\n/g, '\n').replace(/\r/g, '\n').split('\n');

  let currentSubtitle: {
    index?: number;
    startTime?: string;
    endTime?: string;
    textLines: string[];
  } | null = null;

  let lineIndex = 0;

  while (lineIndex < lines.length) {
    const line = lines[lineIndex].trim();

    // Skip empty lines
    if (!line) {
      // If we have a current subtitle with content, save it
      if (currentSubtitle && currentSubtitle.startTime && currentSubtitle.endTime && currentSubtitle.textLines.length > 0) {
        const processedSubtitle = processSubtitleEntry(currentSubtitle);
        if (processedSubtitle) {
          subtitles.push(processedSubtitle);
        }
        currentSubtitle = null;
      }
      lineIndex++;
      continue;
    }

    // Check if this line is a subtitle index (number)
    const indexMatch = line.match(/^\d+$/);
    if (indexMatch) {
      // Save previous subtitle if exists
      if (currentSubtitle && currentSubtitle.startTime && currentSubtitle.endTime && currentSubtitle.textLines.length > 0) {
        const processedSubtitle = processSubtitleEntry(currentSubtitle);
        if (processedSubtitle) {
          subtitles.push(processedSubtitle);
        }
      }

      // Start new subtitle
      currentSubtitle = {
        index: parseInt(indexMatch[0]),
        textLines: []
      };
      lineIndex++;
      continue;
    }

    // Check if this line is a timestamp
    const timeMatch = line.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
    if (timeMatch && currentSubtitle) {
      currentSubtitle.startTime = timeMatch[1];
      currentSubtitle.endTime = timeMatch[2];
      lineIndex++;
      continue;
    }

    // This must be subtitle text
    if (currentSubtitle) {
      currentSubtitle.textLines.push(line);
    }

    lineIndex++;
  }

  // Don't forget the last subtitle
  if (currentSubtitle && currentSubtitle.startTime && currentSubtitle.endTime && currentSubtitle.textLines.length > 0) {
    const processedSubtitle = processSubtitleEntry(currentSubtitle);
    if (processedSubtitle) {
      subtitles.push(processedSubtitle);
    }
  }

  console.log(`Parsed ${subtitles.length} subtitle entries from SRT content`);
  console.log(`Original SRT had ${lines.length} total lines`);

  // Log some debug info if we have fewer subtitles than expected
  if (subtitles.length === 0) {
    console.warn('No subtitles parsed! SRT content preview:', srtContent.substring(0, 200));
    console.warn('Attempting fallback parsing method...');
    return parseSRTContentFallback(srtContent);
  } else if (subtitles.length < 5) {
    console.log('Sample parsed subtitle:', subtitles[0]);
  }

  return subtitles;
};

// Helper function to process a subtitle entry and determine original/translated text
function processSubtitleEntry(entry: {
  index?: number;
  startTime?: string;
  endTime?: string;
  textLines: string[];
}): MergedSubtitle | null {
  if (!entry.startTime || !entry.endTime || entry.textLines.length === 0) {
    return null;
  }

  let originalText = '';
  let translatedText = '';

  // Strategy 1: If we have exactly 2 lines, treat as bilingual (original + translated)
  if (entry.textLines.length === 2) {
    originalText = entry.textLines[0].trim();
    translatedText = entry.textLines[1].trim();
  }
  // Strategy 2: If we have more than 2 lines, try to detect bilingual pattern
  else if (entry.textLines.length > 2) {
    // Check if lines alternate between languages or if there's a clear split
    const midPoint = Math.floor(entry.textLines.length / 2);

    // Try to detect if first half is one language and second half is another
    // Split the lines in half and see if it makes sense as bilingual content
    const firstHalf = entry.textLines.slice(0, midPoint).join('\n').trim();
    const secondHalf = entry.textLines.slice(midPoint).join('\n').trim();

    // If both halves have content, treat as bilingual
    if (firstHalf && secondHalf) {
      originalText = firstHalf;
      translatedText = secondHalf;
    } else {
      // Otherwise, join all lines as original text
      originalText = entry.textLines.join('\n').trim();
      translatedText = '';
    }
  }
  // Strategy 3: Single line - put in original text
  else {
    originalText = entry.textLines[0].trim();
    translatedText = '';
  }

  return {
    id: crypto.randomUUID(),
    startTime: entry.startTime,
    endTime: entry.endTime,
    originalText,
    translatedText,
  };
}

// Fallback parsing method using regex-based approach
function parseSRTContentFallback(srtContent: string): MergedSubtitle[] {
  const subtitles: MergedSubtitle[] = [];

  console.log('Using fallback SRT parsing method...');

  // Use regex to match SRT blocks more flexibly
  const srtBlockRegex = /(\d+)\s*\n\s*(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})\s*\n([\s\S]*?)(?=\n\s*\d+\s*\n|\n\s*$|$)/g;

  let match;
  while ((match = srtBlockRegex.exec(srtContent)) !== null) {
    const [, , startTime, endTime, textContent] = match;

    if (!textContent.trim()) continue;

    // Split text content into lines and filter out empty ones
    const textLines = textContent.split('\n').map(line => line.trim()).filter(line => line);

    let originalText = '';
    let translatedText = '';

    if (textLines.length === 1) {
      originalText = textLines[0];
      translatedText = '';
    } else if (textLines.length === 2) {
      originalText = textLines[0];
      translatedText = textLines[1];
    } else if (textLines.length > 2) {
      // For multiple lines, try to split in half
      const midPoint = Math.floor(textLines.length / 2);
      originalText = textLines.slice(0, midPoint).join('\n');
      translatedText = textLines.slice(midPoint).join('\n');
    }

    subtitles.push({
      id: crypto.randomUUID(),
      startTime,
      endTime,
      originalText,
      translatedText,
    });
  }

  console.log(`Fallback parser found ${subtitles.length} subtitle entries`);
  return subtitles;
}
