
import { MergedSubtitle, SubtitleLine } from '../types';

export const formatSecondsToSRTTime = (totalSeconds: number): string => {
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);
  const milliseconds = Math.floor((totalSeconds * 1000) % 1000);

  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')},${String(milliseconds).padStart(3, '0')}`;
};

const isValidSRTTimeFormat = (timeStr: string): boolean => {
  return /^\d{2}:\d{2}:\d{2},\d{3}$/.test(timeStr);
};

export const generateSRTContent = (subtitles: SubtitleLine[]): string => {
  let srtContent = '';
  subtitles.forEach((sub, index) => {
    if (!isValidSRTTimeFormat(sub.startTime) || !isValidSRTTimeFormat(sub.endTime)) {
      console.warn(`Invalid time format for subtitle ID ${sub.id}. Skipping SRT entry.`);
      return;
    }
    srtContent += `${index + 1}\n`;
    srtContent += `${sub.startTime} --> ${sub.endTime}\n`;
    srtContent += `${sub.text}\n\n`;
  });
  return srtContent;
};

export const downloadSRTFile = (srtContent: string, filename: string = 'subtitles.srt'): void => {
  const blob = new Blob([srtContent], { type: 'text/srt;charset=utf-8;' });
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

// New functions for VTT generation
export const formatSRTTimeToVTTTime = (srtTime: string): string => {
  if (!isValidSRTTimeFormat(srtTime)) {
    // console.warn(`Invalid SRT time format for VTT conversion: ${srtTime}. Returning as is.`);
    return srtTime; // Or handle error appropriately
  }
  return srtTime.replace(',', '.');
};

export const generateVTTContent = (subtitles: MergedSubtitle[], type: 'original' | 'translated'): string => {
  let vttContent = 'WEBVTT\n\n';
  subtitles.forEach((sub, index) => {
    const text = type === 'original' ? sub.originalText : sub.translatedText;
    if (text.trim() === '') {
      return; // Skip empty subtitles
    }
    if (!isValidSRTTimeFormat(sub.startTime) || !isValidSRTTimeFormat(sub.endTime)) {
      console.warn(`Invalid time format for subtitle ID ${sub.id}. Skipping VTT cue.`);
      return;
    }
    vttContent += `${index + 1}\n`; // Optional cue identifier
    vttContent += `${formatSRTTimeToVTTTime(sub.startTime)} --> ${formatSRTTimeToVTTTime(sub.endTime)}\n`;
    vttContent += `${text}\n\n`;
  });
  return vttContent;
};

// Video processing utility functions
export const downloadVideoFile = (videoBlob: Blob, filename: string): void => {
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(videoBlob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const getDefaultSubtitleStyle = () => ({
  fontFamily: 'Arial',
  fontSize: 23,
  fontColor: '#eee711',
  backgroundColor: 'rgba(0,0,0,0.8)',
  position: 'bottom' as const,
  outline: true,
  outlineColor: 'black'
});

// Function to parse SRT content and convert to MergedSubtitle format
export const parseSRTContent = (srtContent: string): MergedSubtitle[] => {
  const subtitles: MergedSubtitle[] = [];

  // Split by double newlines to separate subtitle blocks
  const blocks = srtContent.split(/\n\s*\n/).filter(block => block.trim());

  for (const block of blocks) {
    const lines = block.trim().split('\n');

    // Skip blocks that don't have at least 3 lines (index, timestamp, text)
    if (lines.length < 3) continue;

    // Parse the timestamp line (second line)
    const timeLine = lines[1].trim();
    const timeMatch = timeLine.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);

    if (!timeMatch) continue;

    const [, startTime, endTime] = timeMatch;

    // Combine all text lines (from line 3 onwards)
    const textLines = lines.slice(2);
    const fullText = textLines.join('\n').trim();

    // Check if this is a bilingual subtitle (contains both original and translated text)
    // For bilingual SRT files, we expect each subtitle to have multiple lines
    // We'll try to split the text and assign to original/translated
    let originalText = '';
    let translatedText = '';

    if (textLines.length >= 2) {
      // Assume first line is original, second line is translated
      originalText = textLines[0].trim();
      translatedText = textLines[1].trim();
    } else {
      // Single line - put it in original text
      originalText = fullText;
      translatedText = '';
    }

    subtitles.push({
      id: crypto.randomUUID(),
      startTime,
      endTime,
      originalText,
      translatedText,
    });
  }

  return subtitles;
};
