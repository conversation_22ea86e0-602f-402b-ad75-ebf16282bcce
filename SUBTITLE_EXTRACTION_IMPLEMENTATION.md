# Subtitle Extraction Implementation

## Overview
The subtitle extraction feature allows users to extract existing subtitles from video files using the backend API. When the user clicks the "Extract Video Subtitle" button, the system:

1. Submits a task creation request with `srtOptions.action = "extract"`
2. Polls the task status until completion
3. Downloads the bilingual SRT file when `video_url.endsWith('.srt')`
4. Parses the SRT content and populates the subtitle editor

## Implementation Details

### 1. API Service Methods (`services/apiVideoService.ts`)

#### `extractVideoSubtitles(videoFile, apiKey)`
- Creates FormData with video file and extraction options
- Sends POST request to `/api/capability/task`
- Returns task ID for polling

#### `pollSubtitleExtractionStatus(taskId)`
- Polls task status until completion or timeout
- Checks if `video_url.endsWith('.srt')` for bilingual SRT files
- Downloads and returns SRT content

#### `downloadSRTFile(srtUrl)`
- Fetches SRT file content from the provided URL
- Handles both relative and absolute URLs

### 2. SRT Parsing (`utils/subtitleUtils.ts`)

#### `parseSRTContent(srtContent)`
- Parses SRT format into `MergedSubtitle[]` objects
- Handles bilingual format (2 lines per subtitle entry)
- Handles single language format (1 line per subtitle entry)
- Generates unique IDs for each subtitle entry

### 3. User Interface (`App.tsx`)

#### State Management
```typescript
const [isExtractingSubtitles, setIsExtractingSubtitles] = useState<boolean>(false);
const [extractionTaskId, setExtractionTaskId] = useState<string | null>(null);
```

#### Extract Button
- Orange color scheme to distinguish from other actions
- Disabled when no video file or during other operations
- Shows spinning icon during extraction

#### Progress Display
- Shows "Subtitle Extraction Progress" during extraction
- Orange progress bar for extraction vs. blue for video processing
- Task ID display with copy-to-clipboard functionality

## Usage Flow

1. **User uploads video file**
2. **User clicks "Extract Video Subtitle" button**
3. **System submits extraction request**
   - Creates task with `srtOptions.action = "extract"`
   - Includes API key in srtOptions payload
4. **System polls task status**
   - Shows progress updates
   - Displays task ID for debugging
5. **On completion, system checks file type**
   - If `video_url.endsWith('.srt')`, downloads SRT content
   - Otherwise, shows error
6. **System parses SRT content**
   - Converts to MergedSubtitle format
   - Populates subtitle editor
7. **User can edit extracted subtitles**

## API Request Format

### Task Creation Request
```javascript
FormData {
  videoFile: File,
  srtOptions: JSON.stringify({
    action: "extract",
    apiKey: "user-api-key"
  })
}
```

### Expected Response Format
```javascript
{
  "error": 0,
  "msg": "Task created successfully",
  "data": {
    "task_id": "1748419566_sWhOGwrs"
  },
  "code": 0
}
```

### Task Status Response (Completed)
```javascript
{
  "error": 0,
  "msg": "Success",
  "data": {
    "task_id": "1748419566_sWhOGwrs",
    "status": "completed",
    "process_percent": 100,
    "message": "Extraction completed",
    "video_url": "/path/to/extracted_subtitles.srt"
  },
  "code": 0
}
```

## SRT File Format Support

### Bilingual SRT Format
```
1
00:00:01,500 --> 00:00:04,000
Original text in source language
Translated text in target language

2
00:00:04,500 --> 00:00:07,200
Another original text
Another translated text
```

### Single Language SRT Format
```
1
00:00:01,500 --> 00:00:04,000
Single line of text

2
00:00:04,500 --> 00:00:07,200
Another line of text
```

## Error Handling

- **No video file**: Shows validation error
- **API connection issues**: Shows helpful message about backend server
- **Task timeout**: Shows timeout error after maximum polling attempts
- **Invalid file type**: Shows error if response is not an SRT file
- **Empty SRT**: Shows error if no subtitles found in extracted file

## Integration with Existing Features

- **Progress System**: Uses existing `VideoProcessingProgress` interface
- **API Key Management**: Uses existing API key storage and retrieval
- **Subtitle Editor**: Populates existing `MergedSubtitle[]` state
- **Reset Function**: Clears extraction state along with other data
- **Task ID Display**: Shows alongside video processing task IDs
